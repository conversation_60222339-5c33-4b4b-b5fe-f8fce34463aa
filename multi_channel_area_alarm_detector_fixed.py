#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路实时监控面积警报烟雾检测系统
基于gpu20250514(1)界面效果 + area_alarm_smoke_detector功能
16-Channel Real-time Area Alarm Smoke Detection System
"""

import os
import sys
import time
from datetime import datetime

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

# 导入pygame用于警报音
try:
    import pygame
    pygame.mixer.init()
    HAS_PYGAME = True
    print("✅ pygame音频支持已启用")
except ImportError:
    HAS_PYGAME = False
    print("⚠️ pygame未安装，将使用系统警报音")

# 导入PyQt5
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout,
                           QHBoxLayout, QPushButton, QWidget, QSizePolicy,
                           QComboBox, QScrollArea, QGridLayout, QTextEdit,
                           QMessageBox, QSpacerItem, QSpinBox)
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QThread, pyqtSignal


def write_log(message):
    """写入日志文件"""
    current_directory = os.getcwd()
    file_name = "16路烟雾检测日志.txt"
    file_path = os.path.join(current_directory, file_name)
    
    try:
        with open(file_path, 'a', encoding='utf-8') as file:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            file.write(f"[{timestamp}] {message}\n")
    except Exception as e:
        print(f"Error writing to log file: {e}")


def read_video_paths_from_file(file_path):
    """从文件读取视频路径"""
    try:
        with open(file_path, 'r', encoding="utf-8") as file:
            paths = []
            for line in file:
                line = line.strip()
                if line:
                    paths.append(line)
            return paths
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []


class SquareLabel(QLabel):
    """保持16:9比例的视频显示标签"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(1, 1)
    
    def resizeEvent(self, event):
        super().resizeEvent(event)
        width = self.width()
        height = int(width * 9 / 16)
        self.setFixedHeight(height)


class AreaAlarmVideoThread(QThread):
    """面积警报视频处理线程"""
    
    change_pixmap_signal = pyqtSignal(np.ndarray)
    log_signal = pyqtSignal(str)
    alarm_signal = pyqtSignal(int, float)  # 通道号, 面积
    
    def __init__(self, video_path, thread_id, thread_name):
        super().__init__()
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        self.stop_detection_flag = False
        self.alarm_triggered = False
        
        # 检测视频源类型
        self.is_local_video = self.is_local_video_file(video_path)

        # 初始化视频捕获
        self.cap = None
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.reconnect_delay = 2.0

        # 尝试连接视频源
        if not self.connect_video_source():
            print(f"⚠️ 通道{thread_id}无法连接视频源: {video_path}")
            # 即使连接失败也继续初始化，稍后在run方法中重试
            self.video_width = 640
            self.video_height = 480
            print(f"⚠️ 通道{thread_id}使用默认分辨率: {self.video_width}x{self.video_height}")
        else:
            print(f"✅ 通道{thread_id}视频源连接成功")

        # 获取视频信息
        self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 本地视频特有属性
        if self.is_local_video:
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.current_frame = 0
            print(f"📹 本地视频信息: {self.total_frames}帧, {self.fps:.2f}fps")
        else:
            self.total_frames = 0
            self.fps = 30.0  # 默认FPS
            self.current_frame = 0
        
        # 面积阈值设置
        self.area_threshold_cm2 = 50  # 5平方厘米
        self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        
        # 检测参数
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 500
        self.max_area = 50000
        self.detection_interval = 25  # 检测间隔
        self.contour_threshold = 800   # 轮廓阈值
        
        # 状态变量
        self.frame_count = 0
        self.prev_gray = None
        self.last_alarm_time = 0
        self.alarm_cooldown = 3.0

        # 警报状态控制
        self.is_alarm_playing = False  # 当前是否正在播放警报音
        self.alarm_start_time = 0      # 警报开始时间
        self.max_alarm_duration = 10.0  # 最大警报持续时间（秒）
        
        # 时间标记区域设置（忽略左上角时间）
        self.time_region = self.calculate_time_region()
        
        # 创建输出目录
        self.output_dir = f'detection_results/channel_{thread_id}'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 警报音
        self.alarm_sound = None
        if HAS_PYGAME and os.path.exists('1.mp3'):
            try:
                self.alarm_sound = pygame.mixer.Sound('1.mp3')
                print(f"✅ 通道{thread_id}警报音加载成功")
            except Exception as e:
                print(f"⚠️ 通道{thread_id}警报音加载失败: {e}")
        
        print(f"✅ 通道{thread_id}({thread_name})初始化完成")

    def is_local_video_file(self, video_path):
        """检测是否为本地视频文件"""
        # 检查是否为本地文件路径
        if os.path.exists(video_path):
            return True

        # 检查文件扩展名
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']
        _, ext = os.path.splitext(video_path.lower())
        if ext in video_extensions:
            return True

        # 检查是否为网络流
        if video_path.startswith(('rtsp://', 'rtmp://', 'http://', 'https://')):
            return False

        return False

    def connect_video_source(self):
        """连接视频源"""
        self.connection_attempts += 1

        try:
            if self.is_local_video:
                print(f"📹 通道{self.thread_id}连接本地视频: {self.video_path}")
            else:
                print(f"🌐 通道{self.thread_id}连接RTSP流: {self.video_path} (尝试 {self.connection_attempts}/{self.max_connection_attempts})")

            # 创建VideoCapture对象
            self.cap = cv2.VideoCapture(self.video_path)

            # 设置RTSP连接参数
            if not self.is_local_video:
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲
                self.cap.set(cv2.CAP_PROP_FPS, 25)        # 设置FPS

            # 检查连接是否成功
            if self.cap.isOpened():
                # 尝试读取一帧来验证连接
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    # 获取视频信息
                    self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    if self.video_width > 0 and self.video_height > 0:
                        if self.is_local_video:
                            print(f"✅ 通道{self.thread_id}本地视频连接成功: {self.video_width}x{self.video_height}")
                        else:
                            print(f"✅ 通道{self.thread_id}RTSP流连接成功: {self.video_width}x{self.video_height}")

                        self.connection_attempts = 0  # 重置连接尝试次数
                        return True
                    else:
                        print(f"⚠️ 通道{self.thread_id}视频尺寸无效: {self.video_width}x{self.video_height}")
                else:
                    print(f"⚠️ 通道{self.thread_id}无法读取视频帧")
            else:
                print(f"⚠️ 通道{self.thread_id}无法打开视频源")

        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}连接异常: {e}")

        # 连接失败，清理资源
        if self.cap:
            self.cap.release()
            self.cap = None

        return False

    def reconnect_video_source(self):
        """重新连接视频源"""
        if self.connection_attempts >= self.max_connection_attempts:
            print(f"❌ 通道{self.thread_id}达到最大重连次数，停止尝试")
            return False

        print(f"🔄 通道{self.thread_id}尝试重新连接...")
        time.sleep(self.reconnect_delay)

        return self.connect_video_source()

    def calculate_pixel_area_threshold(self):
        """计算5平方厘米对应的像素面积"""
        # 假设视频覆盖区域：100cm × 60cm = 6000cm²
        total_area_cm2 = 100 * 60
        total_pixels = self.video_width * self.video_height
        pixels_per_cm2 = total_pixels / total_area_cm2
        threshold_pixels = int(self.area_threshold_cm2 * pixels_per_cm2)
        
        print(f"📐 通道{self.thread_id}面积换算: 50cm² = {threshold_pixels}像素")
        return threshold_pixels
    
    def calculate_time_region(self):
        """计算时间标记区域（左上角）"""
        time_width = int(self.video_width * 0.25)
        time_height = int(self.video_height * 0.08)
        return {
            'x': 0,
            'y': 0,
            'width': time_width,
            'height': time_height
        }
    
    def mask_time_region(self, mask):
        """在检测掩码中屏蔽时间区域"""
        time_region = self.time_region
        mask[time_region['y']:time_region['y'] + time_region['height'],
             time_region['x']:time_region['x'] + time_region['width']] = 0
        return mask
    
    def detect_smoke(self, frame):
        """烟雾检测算法（整合面积检测）"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 1. 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
            
            # 2. 运动检测
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]
                
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
            
            # 3. 结合亮度和运动
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)
            
            # 4. 屏蔽时间标记区域
            combined_mask = self.mask_time_region(combined_mask)
            
            # 5. 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 6. 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 7. 分析轮廓并计算总面积
            smoke_regions = []
            total_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        confidence = min(area / 10000, 1.0)
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence,
                            'contour': contour
                        })
                        total_area += area
            
            self.prev_gray = gray.copy()
            return smoke_regions, combined_mask, total_area
            
        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}检测出错: {e}")
            return [], np.zeros_like(frame[:,:,0]), 0
    
    def check_area_alarm(self, total_area):
        """检查是否需要触发面积警报"""
        current_time = time.time()

        if total_area >= self.area_threshold_pixels:
            # 如果当前没有在播放警报音，才触发新的警报
            if not self.is_alarm_playing:
                if current_time - self.last_alarm_time >= self.alarm_cooldown:
                    self.trigger_alarm(total_area)
                    self.last_alarm_time = current_time
                    return True
            else:
                # 检查警报是否已经播放太久，需要停止
                if current_time - self.alarm_start_time >= self.max_alarm_duration:
                    self.stop_alarm_sound()
                    print(f"⏰ 通道{self.thread_id}警报音已达到最大持续时间，自动停止")
        else:
            # 如果检测面积小于阈值，停止警报音
            if self.is_alarm_playing:
                self.stop_alarm_sound()
                print(f"✅ 通道{self.thread_id}检测面积恢复正常，停止警报音")

        return False
    
    def trigger_alarm(self, total_area):
        """触发警报"""
        current_time = time.time()
        self.alarm_triggered = True
        self.is_alarm_playing = True
        self.alarm_start_time = current_time

        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

        # 记录日志
        log_message = f"🚨 通道{self.thread_id}({self.thread_name}) 烟雾警报! 检测面积: {area_cm2:.2f}cm² (阈值: {self.area_threshold_cm2}cm²)"
        print(log_message)
        self.log_signal.emit(log_message)
        write_log(log_message)

        # 发送警报信号
        self.alarm_signal.emit(self.thread_id, area_cm2)

        # 播放警报音（只有在没有播放时才播放）
        if self.alarm_sound and not self.stop_detection_flag and not self.is_alarm_playing_audio():
            try:
                self.alarm_sound.play(-1)  # 循环播放
                print(f"🔊 通道{self.thread_id}开始播放警报音")
            except Exception as e:
                print(f"⚠️ 通道{self.thread_id}播放警报音失败: {e}")
                self.is_alarm_playing = False

    def is_alarm_playing_audio(self):
        """检查警报音是否正在播放"""
        if HAS_PYGAME and self.alarm_sound:
            try:
                # 检查pygame mixer是否正在播放声音
                return pygame.mixer.get_busy()
            except:
                return False
        return False

    def stop_alarm_sound(self):
        """停止警报音"""
        self.is_alarm_playing = False
        self.alarm_triggered = False

        if HAS_PYGAME and self.alarm_sound:
            try:
                # 只停止这个通道的声音
                pygame.mixer.stop()
                print(f"🔇 通道{self.thread_id}警报音已停止")
            except Exception as e:
                print(f"⚠️ 通道{self.thread_id}停止警报音失败: {e}")
    
    def draw_results(self, frame, smoke_regions, total_area):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        # 计算实际面积
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 绘制时间区域标记
        time_region = self.time_region
        cv2.rectangle(result_frame,
                     (time_region['x'], time_region['y']),
                     (time_region['x'] + time_region['width'], 
                      time_region['y'] + time_region['height']),
                     (128, 128, 128), 1)
        cv2.putText(result_frame, "TIME IGNORED",
                   (time_region['x'] + 5, time_region['y'] + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)
        
        # 绘制检测区域
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            area = region['area']
            
            # 根据面积选择颜色
            if total_area >= self.area_threshold_pixels:
                color = (0, 0, 255)  # 红色 - 警报
                thickness = 3
            elif region['confidence'] > 0.7:
                color = (0, 165, 255)  # 橙色 - 高置信度
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色 - 低置信度
                thickness = 2
            
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制区域面积
            region_cm2 = area * self.area_threshold_cm2 / self.area_threshold_pixels
            label = f"Area: {region_cm2:.1f}cm²"
            cv2.putText(result_frame, label, (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 绘制总面积信息
        total_text = f"Total: {area_cm2:.2f}cm² / {self.area_threshold_cm2}cm²"
        if total_area >= self.area_threshold_pixels:
            total_color = (0, 0, 255)  # 红色
            if self.is_alarm_playing:
                status_text = "ALARM PLAYING!"
            else:
                status_text = "ALARM DETECTED!"
        else:
            total_color = (0, 255, 0)  # 绿色
            status_text = "Normal"
        
        cv2.putText(result_frame, total_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, total_color, 2)
        cv2.putText(result_frame, status_text, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, total_color, 2)
        
        # 绘制通道信息
        if self.is_local_video:
            # 本地视频显示播放进度
            progress = (self.current_frame / self.total_frames * 100) if self.total_frames > 0 else 0
            channel_text = f"CH{self.thread_id}: {self.thread_name} [{progress:.1f}%]"
        else:
            # 网络流显示帧数
            channel_text = f"CH{self.thread_id}: {self.thread_name} [Frame:{self.frame_count}]"

        cv2.putText(result_frame, channel_text, (10, self.video_height - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 本地视频额外显示循环标识
        if self.is_local_video:
            loop_text = f"LOCAL VIDEO (Loop)"
            cv2.putText(result_frame, loop_text, (10, self.video_height - 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        
        # 绘制停止警报按钮（如果警报激活）
        if self.alarm_triggered:
            button_x = self.video_width - 120
            button_y = 10
            button_w = 110
            button_h = 30
            
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (0, 0, 255), -1)
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (255, 255, 255), 2)
            cv2.putText(result_frame, "STOP ALARM", (button_x + 5, button_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame

    def save_alarm_image(self, frame, total_area):
        """保存警报图像到results文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

        filename = f"ALARM_CH{self.thread_id+1}_{timestamp}_area_{area_cm2:.2f}cm2.jpg"
        filepath = os.path.join(self.output_dir, filename)

        # 在图像上添加警报信息
        alarm_frame = frame.copy()
        alarm_text = f"SMOKE ALARM CH{self.thread_id} - Area: {area_cm2:.2f}cm²"
        time_text = f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        cv2.putText(alarm_frame, alarm_text, (50, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        cv2.putText(alarm_frame, time_text, (50, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

        cv2.imwrite(filepath, alarm_frame)
        print(f"💾 通道{self.thread_id}警报图像已保存: {filename}")

        return filepath

    def update_params(self, detection_interval=None, contour_threshold=None,
                     area_threshold=None, brightness_min=None, brightness_max=None):
        """更新检测参数"""
        if detection_interval is not None:
            self.detection_interval = detection_interval
        if contour_threshold is not None:
            self.contour_threshold = contour_threshold
        if area_threshold is not None:
            self.area_threshold_cm2 = area_threshold
            self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        if brightness_min is not None:
            self.brightness_min = brightness_min
        if brightness_max is not None:
            self.brightness_max = brightness_max

    def run(self):
        """主运行循环"""
        # 如果初始化时连接失败，在这里重新尝试连接
        if self.cap is None or not self.cap.isOpened():
            print(f"🔄 通道{self.thread_id}尝试重新连接视频源...")
            if not self.connect_video_source():
                print(f"❌ 通道{self.thread_id}连接失败，线程退出")
                return

        while self._run_flag:
            if self.cap is None or not self.cap.isOpened():
                print(f"⚠️ 通道{self.thread_id}视频源未连接，尝试重新连接...")
                if not self.reconnect_video_source():
                    print(f"❌ 通道{self.thread_id}重连失败，线程退出")
                    break
                continue

            ret, frame = self.cap.read()
            if not ret:
                if self.is_local_video:
                    # 本地视频播放完毕，重新开始循环播放
                    print(f"🔄 通道{self.thread_id}本地视频播放完毕，重新开始循环播放")
                    if self.cap and self.cap.isOpened():
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到第一帧
                        self.current_frame = 0
                        ret, frame = self.cap.read()
                        if not ret:
                            print(f"⚠️ 通道{self.thread_id}无法重新读取视频")
                            break
                    else:
                        print(f"⚠️ 通道{self.thread_id}视频源已断开")
                        break
                else:
                    # RTSP流重新连接
                    print(f"🔄 通道{self.thread_id}RTSP流断开，尝试重新连接...")
                    if self.cap:
                        self.cap.release()
                        self.cap = None

                    # 尝试重新连接
                    if self.reconnect_video_source():
                        print(f"✅ 通道{self.thread_id}重新连接成功")
                        continue
                    else:
                        print(f"❌ 通道{self.thread_id}重新连接失败")
                        break

            self.frame_count += 1
            if self.is_local_video:
                self.current_frame += 1

            # 根据检测间隔进行检测
            if self.frame_count % self.detection_interval == 0 and not self.stop_detection_flag:
                try:
                    # 检测烟雾
                    smoke_regions, _, total_area = self.detect_smoke(frame)

                    # 检查面积警报
                    alarm_triggered = self.check_area_alarm(total_area)

                    # 绘制结果
                    result_frame = self.draw_results(frame, smoke_regions, total_area)

                    # 如果触发警报，保存图像
                    if alarm_triggered:
                        self.save_alarm_image(result_frame, total_area)

                    # 发送帧信号
                    self.change_pixmap_signal.emit(result_frame)

                except Exception as e:
                    print(f"⚠️ 通道{self.thread_id}处理帧出错: {e}")
                    self.change_pixmap_signal.emit(frame)
            else:
                self.change_pixmap_signal.emit(frame)

            # 根据视频类型调整播放速度
            if self.is_local_video and self.fps > 0:
                time.sleep(1.0 / self.fps)  # 按原始FPS播放
            else:
                time.sleep(0.03)  # 约30fps

    def stop(self):
        """停止线程"""
        self._run_flag = False
        self.stop_detection_flag = True

        # 停止警报音和清理警报状态
        self.stop_alarm_sound()

        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()

        self.quit()
        self.wait()


class MainWindow(QMainWindow):
    """16路实时监控面积警报烟雾检测主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle('16路实时监控面积警报烟雾检测系统 V2.0')
        self.setGeometry(100, 100, 1600, 1000)

        # 初始化pygame
        if HAS_PYGAME:
            pygame.mixer.init()

        # 系统配置
        self.txt_file_path = 'video_paths.txt'
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 读取视频路径
        self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
        if not self.read_video_paths:
            # 创建默认配置文件
            print("⚠️ 未找到video_paths.txt配置文件，创建默认配置...")
            self.create_default_video_paths()
            self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
        else:
            print(f"✅ 成功读取配置文件: {self.txt_file_path}")
            self.analyze_video_sources()

        # 计算通道数（每两行为一个通道：路径+名称）
        self.num_channels = len(self.read_video_paths) // 2
        if self.num_channels > 16:
            self.num_channels = 16  # 限制最大16路

        # 初始化线程和UI组件
        self.video_thread = [None] * self.num_channels
        self.image_label = [None] * self.num_channels
        self.number_label = [None] * self.num_channels

        # 日志窗口
        self.log_window = None

        # 初始化UI
        self.initUI()

        print(f"✅ 16路监控系统初始化完成，支持{self.num_channels}个通道")

    def analyze_video_sources(self):
        """分析视频源类型"""
        rtsp_count = 0
        local_count = 0
        other_count = 0

        print("🔍 分析视频源配置:")
        for i in range(0, len(self.read_video_paths), 2):
            if i + 1 < len(self.read_video_paths):
                video_path = self.read_video_paths[i]
                channel_name = self.read_video_paths[i + 1]
                channel_num = (i // 2) + 1

                if video_path.startswith(('rtsp://', 'rtmp://')):
                    rtsp_count += 1
                    print(f"   通道{channel_num:2d}: RTSP流 - {channel_name}")
                elif os.path.exists(video_path):
                    local_count += 1
                    size_mb = os.path.getsize(video_path) / (1024*1024)
                    print(f"   通道{channel_num:2d}: 本地视频 - {video_path} ({size_mb:.1f}MB)")
                else:
                    other_count += 1
                    print(f"   通道{channel_num:2d}: 其他源 - {video_path}")

        print(f"📊 视频源统计:")
        print(f"   🌐 RTSP网络流: {rtsp_count} 路")
        print(f"   📹 本地视频: {local_count} 路")
        print(f"   ❓ 其他源: {other_count} 路")

        if rtsp_count > 0:
            print("🚀 检测到RTSP流，系统将进入实时监控模式")
            print("📋 RTSP连接提示:")
            print("   - 确保网络连接正常")
            print("   - 验证相机IP地址和端口")
            print("   - 检查用户名和密码")
            print("   - 系统将自动重连断开的流")

    def create_default_video_paths(self):
        """创建默认RTSP监控相机配置文件"""
        default_paths = []

        print("🔧 创建默认RTSP监控相机配置...")

        # 创建16路RTSP监控相机配置
        for i in range(16):
            # 生成RTSP地址 (可根据实际情况修改)
            rtsp_url = f"rtsp://admin:admin@192.168.1.{200+i}:554/Streaming/Channels/101"
            channel_name = f"监控相机{i+1}"

            default_paths.append(rtsp_url)
            default_paths.append(channel_name)
            print(f"   通道{i+1:2d}: {rtsp_url}")

        try:
            with open(self.txt_file_path, 'w', encoding='utf-8') as file:
                for path in default_paths:
                    file.write(path + '\n')
            print(f"✅ 创建默认RTSP配置文件: {self.txt_file_path}")
            print(f"   🌐 RTSP监控通道: 16")
            print("📋 配置说明:")
            print("   - 请根据实际监控相机修改IP地址")
            print("   - 请根据相机品牌修改RTSP路径")
            print("   - 请修改用户名和密码")
            print("   - 支持海康威视、大华、宇视等主流品牌")
        except Exception as e:
            print(f"⚠️ 创建配置文件失败: {e}")

    def create_pro1_test_config(self):
        """创建Pro1测试视频配置"""
        default_paths = []

        # 检查Pro1中的1.mp4和2.mp4文件
        pro1_videos = ['1.mp4', '2.mp4']
        available_videos = []

        print("🔍 检查Pro1中的测试视频文件:")
        for video in pro1_videos:
            if os.path.exists(video):
                size_mb = os.path.getsize(video) / (1024*1024)
                available_videos.append(video)
                print(f"   ✅ 发现 {video} ({size_mb:.1f}MB)")
            else:
                print(f"   ❌ 未找到 {video}")

        if available_videos:
            print(f"🎬 使用 {len(available_videos)} 个Pro1测试视频配置16路监控")

            # 使用Pro1的1.mp4和2.mp4配置16路监控
            for i in range(16):
                # 交替使用1.mp4和2.mp4
                video_path = available_videos[i % len(available_videos)]
                default_paths.append(video_path)

                # 为每个通道创建独特的名称
                if video_path == '1.mp4':
                    channel_name = f"Pro1测试视频A-通道{i+1}"
                else:
                    channel_name = f"Pro1测试视频B-通道{i+1}"

                default_paths.append(channel_name)
                print(f"   通道{i+1:2d}: {video_path} -> {channel_name}")

            try:
                with open(self.txt_file_path, 'w', encoding='utf-8') as file:
                    for path in default_paths:
                        file.write(path + '\n')
                print(f"✅ 创建Pro1测试配置文件: {self.txt_file_path}")
                print(f"   📹 本地视频通道: 16 (使用{len(available_videos)}个视频文件)")
                return True
            except Exception as e:
                print(f"⚠️ 创建配置文件失败: {e}")

        return False

    def initUI(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel('烟雾实时监控检测系统 V2.0')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin: 10px;")
        main_layout.addWidget(title_label)

        # 视频网格
        video_widget = QWidget()
        video_layout = QGridLayout(video_widget)

        # 创建16个视频显示区域
        for i in range(self.num_channels):
            # 创建容器widget
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(2, 2, 2, 2)
            container_layout.setSpacing(2)

            # 通道编号标签
            self.number_label[i] = QLabel(f"{i+1}")
            self.number_label[i].setAlignment(Qt.AlignCenter)
            self.number_label[i].setStyleSheet("""
                QLabel {
                    color: green;
                    font-size: 16px;
                    font-weight: bold;
                    background-color: rgba(255, 255, 255, 200);
                    border: 1px solid black;
                    min-width: 25px;
                    max-width: 25px;
                    min-height: 25px;
                    max-height: 25px;
                }
            """)

            # 视频显示标签
            self.image_label[i] = SquareLabel()
            self.image_label[i].setAlignment(Qt.AlignCenter)
            self.image_label[i].setText(f"通道 {i+1}\n未连接")
            self.image_label[i].setStyleSheet("""
                QLabel {
                    background-color: #2c3e50;
                    color: white;
                    border: 2px solid #34495e;
                    font-size: 12pt;
                }
            """)

            # 添加到容器
            container_layout.addWidget(self.number_label[i], 0, Qt.AlignCenter)
            container_layout.addWidget(self.image_label[i], 1)

            # 添加到网格
            row = i // 4
            col = i % 4
            video_layout.addWidget(container, row, col)

        main_layout.addWidget(video_widget)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton('开始全部检测')
        self.start_btn.clicked.connect(self.start_detection)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)

        self.stop_btn = QPushButton('全部停止警报')
        self.stop_btn.clicked.connect(self.stop_all_alarms)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # Pro1测试模式按钮
        self.pro1_test_btn = QPushButton('Pro1视频测试模式')
        self.pro1_test_btn.clicked.connect(self.start_pro1_test_mode)
        self.pro1_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # RTSP实时监控模式按钮
        self.rtsp_mode_btn = QPushButton('RTSP实时监控模式')
        self.rtsp_mode_btn.clicked.connect(self.start_rtsp_mode)
        self.rtsp_mode_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.pro1_test_btn)
        button_layout.addWidget(self.rtsp_mode_btn)

        main_layout.addLayout(button_layout)

    def start_detection(self):
        """开始检测"""
        started_count = 0

        for i in range(self.num_channels):
            if self.video_thread[i] is None:
                try:
                    # 获取视频路径和名称
                    video_path = self.read_video_paths[i * 2]
                    thread_name = self.read_video_paths[i * 2 + 1]

                    # 创建线程
                    self.video_thread[i] = AreaAlarmVideoThread(video_path, i, thread_name)

                    # 连接信号
                    self.video_thread[i].change_pixmap_signal.connect(
                        lambda frame, j=i: self.update_image(frame, j)
                    )
                    self.video_thread[i].alarm_signal.connect(self.handle_alarm)

                    # 启动线程
                    self.video_thread[i].start()
                    started_count += 1

                    print(f"✅ 启动通道{i}: {thread_name}")

                except Exception as e:
                    print(f"⚠️ 启动通道{i}失败: {e}")

        print(f"✅ 已启动 {started_count} 个通道的检测")

    def start_pro1_test_mode(self):
        """启动Pro1视频测试模式"""
        print("🎬 启动Pro1视频测试模式...")

        # 停止现有的检测
        self.stop_all_alarms()

        # 等待线程停止
        for i in range(len(self.video_thread)):
            if self.video_thread[i] is not None:
                self.video_thread[i].stop()
                self.video_thread[i] = None

        # 创建Pro1测试配置
        if self.create_pro1_test_config():
            # 重新读取配置
            self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
            self.num_channels = len(self.read_video_paths) // 2
            if self.num_channels > 16:
                self.num_channels = 16

            # 启动检测
            self.start_detection()

            print("� Pro1测试功能说明:")
            print("   - 所有检测功能与实时视频完全相同")
            print("   - 面积阈值警报：5平方厘米")
            print("   - 时间区域忽略：左上角时间标记")
            print("   - 不重复报警：智能警报管理")
            print("   - 循环播放：视频播放完毕自动重新开始")
            print("   - 警报音：1.mp3文件")
            print("   - 图像保存：detection_results文件夹")
        else:
            print("❌ Pro1测试模式失败：未找到测试视频文件")

    def start_rtsp_mode(self):
        """启动RTSP实时监控模式"""
        print("🌐 启动RTSP实时监控模式...")

        # 停止现有的检测
        self.stop_all_alarms()

        # 等待线程停止
        for i in range(len(self.video_thread)):
            if self.video_thread[i] is not None:
                self.video_thread[i].stop()
                self.video_thread[i] = None

        # 创建RTSP配置
        self.create_default_video_paths()

        # 重新读取配置
        self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
        self.num_channels = len(self.read_video_paths) // 2
        if self.num_channels > 16:
            self.num_channels = 16

        print("📋 RTSP配置已创建，请修改video_paths.txt中的IP地址和认证信息")
        print("📋 修改完成后点击'开始全部检测'启动实时监控")

    def update_image(self, frame, channel_id):
        """更新视频显示"""
        try:
            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)

            if channel_id < len(self.image_label) and self.image_label[channel_id]:
                scaled_pixmap = pixmap.scaled(
                    self.image_label[channel_id].size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                self.image_label[channel_id].setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"⚠️ 更新通道{channel_id}图像失败: {e}")

    def handle_alarm(self, channel_id, area_cm2):
        """处理警报信号"""
        try:
            # 更新通道标签颜色为红色
            if channel_id < len(self.number_label) and self.number_label[channel_id]:
                self.number_label[channel_id].setStyleSheet("""
                    QLabel {
                        color: white;
                        background-color: red;
                        font-size: 20px;
                        font-weight: bold;
                        border: 2px solid red;
                        min-width: 30px;
                        max-width: 30px;
                        min-height: 30px;
                        max-height: 30px;
                    }
                """)

            print(f"🚨 通道{channel_id+1} 烟雾警报！检测面积: {area_cm2:.2f}cm²")

        except Exception as e:
            print(f"⚠️ 处理通道{channel_id}警报失败: {e}")

    def stop_all_alarms(self):
        """停止全部警报"""
        stopped_count = 0

        for i in range(len(self.video_thread)):
            if self.video_thread[i] is not None:
                try:
                    # 停止警报音
                    if self.video_thread[i].alarm_triggered or self.video_thread[i].is_alarm_playing:
                        self.video_thread[i].stop_alarm_sound()

                    # 恢复通道标签颜色
                    if i < len(self.number_label) and self.number_label[i]:
                        self.number_label[i].setStyleSheet("""
                            QLabel {
                                color: green;
                                font-size: 20px;
                                font-weight: bold;
                                background-color: rgba(255, 255, 255, 200);
                                border: 1px solid black;
                                min-width: 30px;
                                max-width: 30px;
                                min-height: 30px;
                                max-height: 30px;
                            }
                        """)

                    stopped_count += 1

                except Exception as e:
                    print(f"⚠️ 停止通道{i}失败: {e}")

        print(f"✅ 已停止 {stopped_count} 个通道的警报")

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            # 停止所有视频线程
            for i in range(len(self.video_thread)):
                if self.video_thread[i]:
                    self.video_thread[i].stop()

            # 清理pygame
            if HAS_PYGAME:
                pygame.mixer.quit()
                pygame.quit()

            write_log("窗口关闭，系统退出")
            super().closeEvent(event)

        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            event.accept()


if __name__ == '__main__':
    app = QApplication(sys.argv)

    try:
        main_window = MainWindow()
        main_window.show()

        write_log("16路监控面积警报烟雾检测系统启动")
        print("🚀 16路监控面积警报烟雾检测系统启动成功")

        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        sys.exit(1)
